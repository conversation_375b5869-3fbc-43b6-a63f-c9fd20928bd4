import datetime as dt
from decimal import Decimal
from functools import cached_property
from typing import Any, Optional, Union

# Prefix used for XBRL elements in Italian financial statements
XBRL_PREFIX = "itcc-ci:"


def is_xbrl_item(value: Any) -> bool:
    return isinstance(value, dict) and "$" in value


def is_xbrl_list(value: Any) -> bool:
    return isinstance(value, list) and value and isinstance(value[0], dict)


class XBRLDirectAccessError(AttributeError):
    def __init__(self, class_name: str):
        message = (
            f"Direct attribute access not allowed. "
            f"Use indexing instead: {class_name}[index].attribute_name"
        )
        super().__init__(message)


class XBRLAttributeAccessMixin:
    """Mixin providing common XBRL attribute access logic with prefix handling.

    Classes using this mixin must have:
    - self.data: dict containing the XBRL data
    - self.get_full_data(): method returning the full XBRL data for context
    """

    def get_full_data(self) -> dict[str, Any]:
        """Return the full XBRL data. Must be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement get_full_data()")

    def __getattr__(self, name: str) -> Any:
        """Access attributes by name, with special handling for Italian XBRL prefixes.

        Supports both direct attribute names and prefixed names (itcc-ci:).
        """
        # Try with itcc-ci: prefix first (most common case for XBRL data)
        prefixed_name = f"{XBRL_PREFIX}{name}"
        if prefixed_name in self.data:
            value = self.data[prefixed_name]
            full_data = self.get_full_data()
            if is_xbrl_item(value):
                # Return an XBRLItem for dictionary with $ key
                return XBRLItem(value, full_data)
            if is_xbrl_list(value):
                # Return a list DTO for nested access
                return XBRLList(value, full_data)
            # This case should never happen in valid XBRL data
            raise ValueError(
                f"Unexpected XBRL data structure for attribute '{name}'. "
                f"Expected XBRL item (dict with '$' key) or XBRL list, "
                f"but got: {type(value).__name__}"
            )

        # Try direct match if prefixed name not found
        if name in self.data:
            return self.data[name]

        msg = f"'{self.__class__.__name__}' object has no attribute '{name}'"
        raise AttributeError(msg)


class XBRLUnitDTO:
    """DTO for XBRL unit data, providing structured access to unit information.

    Example of self.data structure:
    {
        "@id": "Valuta",
        "measure": ["iso4217:EUR"]
    }
    """

    def __init__(self, data: dict[str, Any]):
        self.data = data

    @property
    def id(self) -> str:
        """Returns the unit ID."""
        return self.data.get("@id", "")

    @property
    def measures(self) -> list[str]:
        """Returns the list of measures for this unit."""
        return self.data.get("measure", [])

    @property
    def is_numeric(self) -> bool:
        """Returns True if this unit represents a numeric value (EUR or shares)."""
        return any(
            isinstance(m, str) and (m.startswith("iso4217:") or m == "shares")
            for m in self.measures
        )

    def __repr__(self) -> str:
        return f"XBRLUnitDTO(id={self.id}, measures={self.measures})"

    def __str__(self) -> str:
        return self.__repr__()


class XBRLItem:
    """XBRL item with attributes, providing access to main value and attributes.

    Example of self.data structure:
    {
        "$": 3726054.0,
        "@contextRef": "IstantEserCorr",
        "@decimals": 0,
        "@unitRef": "Valuta"
    }

    Example of self.full_data structure:
    {
        "unit": [
            {"@id": "Valuta", "measure": ["iso4217:EUR"]},
            {"@id": "shares", "measure": ["shares"]}
        ],
        "context": [
            {
                "@id": "IstantEserCorr",
                "entity": {"identifier": {"$": "12681660960", "@scheme": "http://www.infocamere.it"}},
                "period": {"instant": "2023-12-31"}
            }
        ],
        ... # other XBRL data
    }
    """

    def __init__(self, data: dict, full_data: dict):
        self.data = data
        self.full_data = full_data

    @property
    def value(self) -> Any:
        """Returns the main value ($) as Decimal if @unitRef is numeric.

        Non-numeric values are returned as is.
        All numeric values (int, float) are converted to Decimal when they have a
        numeric unit reference.
        """
        raw_value = self.data.get("$")
        unit_ref = self.data.get("@unitRef")

        if unit_ref and raw_value is not None:
            # Find the unit definition using the helper method
            unit = self.get_unit_by_id(unit_ref)
            if unit and unit.is_numeric:
                try:
                    # Ensure all numeric values (int, float) are converted to Decimal
                    if isinstance(raw_value, (int, float)) or (
                        isinstance(raw_value, str)
                        and raw_value.strip().replace(".", "", 1).isdigit()
                    ):
                        return Decimal(str(raw_value))
                except (ValueError, TypeError):
                    pass

        # Return the raw value if not numeric or conversion failed
        return raw_value

    @property
    def is_numeric(self) -> bool:
        """Returns True if this value is numeric based on @unitRef."""
        unit_ref = self.data.get("@unitRef")
        if unit_ref:
            unit = self.get_unit_by_id(unit_ref)
            return unit.is_numeric if unit else False
        return False

    @cached_property
    def units(self) -> list[XBRLUnitDTO]:
        """Returns all units defined in the XBRL document."""
        return [XBRLUnitDTO(unit) for unit in self.full_data.get("unit", [])]

    @property
    def context(self) -> list[dict[str, Any]]:
        """Returns the list of contexts."""
        return self.full_data.get("context", [])

    def get_unit_by_id(self, unit_id: str) -> Optional[XBRLUnitDTO]:
        """Returns the unit with the specified ID, or None if not found."""
        return next((unit for unit in self.units if unit.id == unit_id), None)

    def __getattr__(self, name: str) -> Any:
        """Access attributes by name."""
        if name in self.data:
            return self.data[name]
        msg = f"'{self.__class__.__name__}' object has no attribute '{name}'"
        raise AttributeError(msg)

    def __getitem__(self, key: str) -> Any:
        """Dictionary-like access: item["$"]."""
        return self.data.get(key)

    def __repr__(self) -> str:
        return repr(self.data)

    def __str__(self) -> str:
        return str(self.data)


class XBRLListItem(XBRLAttributeAccessMixin):
    """DTO for a single XBRL list item, providing attribute access to dictionary keys.

    Example of self.data structure:
    {
        "itcc-ci:VarieAltreRiserveImporto": [
            {
                "$": 4704713.0,
                "@unitRef": "Valuta",
                "@decimals": 0,
                "@contextRef": "IstantEserCorr"
            }
        ],
        "itcc-ci:VarieAltreRiserveDescrizione": [
            {
                "$": "Riserva finanziamento convertendo",
                "@contextRef": "IstantEserCorr"
            }
        ]
    }
    """

    def __init__(self, data: dict[str, Any], full_data: dict[str, Any]):
        self.data = data
        self.full_data = full_data

    def get_full_data(self) -> dict[str, Any]:
        """Return the full XBRL data for context."""
        return self.full_data

    def __getitem__(self, key: str) -> Any:
        """Dictionary-like access: item["key"]."""
        return self.data.get(key)

    def __contains__(self, key: str) -> bool:
        """Support for 'in' operator."""
        return key in self.data

    def __repr__(self) -> str:
        return repr(self.data)

    def __str__(self) -> str:
        return str(self.data)


class XBRLList:
    """DTO for XBRL list data, providing intuitive access to list elements.

    Example of self.data structure:
    [
        {
            "itcc-ci:VarieAltreRiserveImporto": [
                {
                    "$": 4704713.0,
                    "@unitRef": "Valuta",
                    "@decimals": 0,
                    "@contextRef": "IstantEserCorr"
                }
            ],
            "itcc-ci:VarieAltreRiserveDescrizione": [
                {
                    "$": "Riserva finanziamento convertendo",
                    "@contextRef": "IstantEserCorr"
                }
            ]
        },
        {
            "itcc-ci:VarieAltreRiserveDescrizione": [
                {"@contextRef": "IstantEserCorr"}
            ]
        }
    ]
    """

    def __init__(self, data: list[dict[str, Any]], full_data: dict[str, Any]):
        self.data = data
        self.full_data = full_data

    def __getattr__(self, _: str) -> Any:
        """Prevent direct attribute access, use indexing instead."""
        raise XBRLDirectAccessError(self.__class__.__name__)

    def __getitem__(self, index: int) -> Union[XBRLListItem, Any]:
        """Access list elements by index, wrapping dictionaries in XBRLListItem."""
        item = self.data[index]
        if is_xbrl_item(item):
            return XBRLItem(item, self.full_data)
        if is_xbrl_list(item):
            return XBRLList(item, self.full_data)
        if isinstance(item, dict):
            return XBRLListItem(item, self.full_data)
        return item

    def __len__(self) -> int:
        """Return the length of the list."""
        return len(self.data)

    def __iter__(self):
        """Iterate over the list elements, wrapping dictionaries in XBRLListItem."""
        for item in self.data:
            if isinstance(item, dict):
                yield XBRLListItem(item, self.full_data)
            else:
                yield item

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}({len(self.data)} items)"


class XBRLDataDTO(XBRLAttributeAccessMixin):
    """DTO for XBRL data, providing intuitive access to XBRL elements.

    Example of self.data structure:
    {
        "context": [
            {
                "@id": "IstantEserCorr",
                "entity": {
                    "identifier": {
                        "$": "12681660960",
                        "@scheme": "http://www.infocamere.it"
                    }
                },
                "period": {"instant": "2023-12-31"},
                "scenario": {"itcc-ci:scen": ["itcc-ci:Depositato"]}
            }
        ],
        "unit": [
            {"@id": "Valuta", "measure": ["iso4217:EUR"]},
            {"@id": "shares", "measure": ["shares"]},
            {"@id": "pure", "measure": ["pure"]}
        ],
        "itcc-ci:TotaleDisponibilitaLiquide": {
            "$": 3726054.0,
            "@contextRef": "IstantEserCorr",
            "@decimals": 0,
            "@unitRef": "Valuta"
        },
        "itcc-ci:VarieAltreRiserve": [
            {
                "itcc-ci:VarieAltreRiserveImporto": [...],
                "itcc-ci:VarieAltreRiserveDescrizione": [...]
            },
            {...}
        ]
    }
    """

    def __init__(self, data: dict):
        self.data = data

    def get_full_data(self) -> dict[str, Any]:
        """Return the full XBRL data for context."""
        return self.data

    @cached_property
    def context_periods(self) -> dict[str, Any]:
        return {
            context_item["@id"]: context_item["period"] for context_item in self.context
        }

    @cached_property
    def context_years(self) -> dict[str, int]:
        return {
            context_id: dt.datetime.fromisoformat(
                period.get("instant", period.get("endDate"))
            ).year
            for context_id, period in self.context_periods.items()
        }

    @cached_property
    def main_year(self) -> int:
        return max(self.context_years.values())

    @cached_property
    def main_context_ids(self) -> list[str]:
        return [
            context_id
            for context_id, year in self.context_years.items()
            if year == self.main_year
        ]

    @property
    def items(self) -> dict[str, Any]:
        """Returns a simplified dictionary representation of the XBRL data.

        Only includes keys that have the XBRL_PREFIX.
        """
        result = {}

        for key, value in self.data.items():
            # Skip internal XBRL structures like context and unit
            if key in ["context", "unit"] or not key.startswith(XBRL_PREFIX):
                continue

            clean_key = key.replace(XBRL_PREFIX, "")
            result[clean_key] = self._process_item(value)

        return result

    def _get_item_by_main_contexts(self, items: list[dict]) -> dict | list[dict]:
        if all("@contextRef" in item for item in items):
            return next(
                (
                    item
                    for item in items
                    if item["@contextRef"] in self.main_context_ids
                ),
                None,
            )
        return None

    def _process_item(self, item: Any) -> Any:  # noqa: PLR0911
        """Recursively process XBRL items to extract simplified values."""

        # Case 1: Direct XBRL item with $ key
        if is_xbrl_item(item):
            return XBRLItem(item, self.data).value

        # Case 2: List of dictionaries - process each dictionary
        if is_xbrl_list(item):
            if all(is_xbrl_item(list_item) for list_item in item):
                if context_item := self._get_item_by_main_contexts(item):
                    return XBRLItem(context_item, self.data).value
                return [XBRLItem(list_item, self.data).value for list_item in item]
            return [self._process_dict(list_item) for list_item in item]

        # Case 3: Dictionary with XBRL items
        if isinstance(item, dict):
            return self._process_dict(item)

        # Case 4: List of other types
        if isinstance(item, list):
            return [self._process_item(list_item) for list_item in item]

        # Case 5: Simple value
        return item

    def _process_dict(self, item_dict: dict) -> dict:
        """Process a dictionary of XBRL items."""
        result = {}

        for key, value in item_dict.items():
            clean_key = key.replace(XBRL_PREFIX, "")
            processed_value = self._process_item(value)
            result[clean_key] = processed_value

        return result

    def __repr__(self) -> str:
        return f"XBRLDataDTO({len(self.data)} items)"
