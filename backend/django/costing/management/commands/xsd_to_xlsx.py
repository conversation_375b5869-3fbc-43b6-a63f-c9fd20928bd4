from django.core.management import BaseCommand

from costing.engine.xsd_to_xlsx import xsd_to_xlsx


class Command(BaseCommand):
    help = "Recreate DB, optionally generating fake data"

    def add_arguments(self, parser):
        parser.add_argument("xsd_filepath", help="XSD file path")
        parser.add_argument("xlsx_filepath", help="XLSX file path")
        parser.add_argument("--main_sheet_name")
        parser.add_argument("--sheet_prefix")

    def handle(self, *args, **options):
        xsd_filepath = options["xsd_filepath"]
        xlsx_filepath = options["xlsx_filepath"]
        main_sheet_name = options.get("main_sheet_name", "main")
        sheet_prefix = options.get("sheet_prefix")

        xsd_to_xlsx(
            xsd_filepath=xsd_filepath,
            xlsx_filepath=xlsx_filepath,
            main_sheet_name=main_sheet_name,
            sheet_prefix=sheet_prefix,
        )
