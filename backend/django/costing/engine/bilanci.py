import datetime
from typing import Any

from costing.engine.base import process
from costing.engine.utils import cell_ref


def compute_bilanci(*, year: int) -> dict[Any, Any]:
    return process(
        template_file="costing/engine/templates/ModelloAnalisiBilanci.xlsx",
        inputs={cell_ref("E2"): datetime.datetime(year, 12, 31)},  # noqa: DTZ001
        outputs={
            **{
                f"{year + t}": {
                    "Ricavi": cell_ref(t + 9, 12),
                    "Ebit": cell_ref(t + 9, 13),
                    "RisultatoNetto": cell_ref(t + 9, 14),
                    "AttivoFisso": cell_ref(t + 9, 15),
                    "CapitaleCircolanteNetto": cell_ref(t + 9, 16),
                }
                for t in range(-5, 7)
            }
        },
    )
