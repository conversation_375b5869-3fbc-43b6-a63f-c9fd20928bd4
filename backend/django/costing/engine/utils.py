from openpyxl.utils.cell import get_column_letter


def cell_ref(*args) -> str:
    if len(args) == 1 and isinstance(args[0], str):
        return f"'Excel -> App'!{args[0]}"
    if len(args) == 2 and isinstance(args[0], int) and isinstance(args[1], int):
        return f"'Excel -> App'!{get_column_letter(args[0])}{args[1]}"
    raise ValueError(f"Invalid arguments: {args}. Expected (str) or (int, int).")
