from dataclasses import dataclass
from enum import StrEnum
from typing import Optional

import xmlschema
from openpyxl import Workbook

from costing.engine.constants import YEAR_LABEL


def __get_element_name(element):
    return element.display_name.split(":")[-1]


def __get_field_type(element):
    content_type = element.type.content
    if hasattr(content_type, "model") and content_type.model == "sequence":
        return FieldType.SEQUENCE
    return FieldType.COMMON


class FieldType(StrEnum):
    COMMON = "COMMON"
    SEQUENCE = "SEQUENCE"


@dataclass(frozen=True)
class XSDField:
    name: str
    type: FieldType
    children: Optional[list[str]] = None


def extract_xsd_fields(xsd_filepath: str) -> list[XSDField]:
    schema = xmlschema.XMLSchema(xsd_filepath)
    fields = []
    for element in schema.root_elements:
        element_name = __get_element_name(element)
        field_type = __get_field_type(element)
        children = (
            [__get_element_name(child) for child in element.iterchildren()]
            if field_type == FieldType.SEQUENCE
            else None
        )
        fields.append(
            XSDField(
                name=element_name,
                type=field_type,
                children=children,
            )
        )
    return fields


def create_xlsx_template(
    xlsx_filepath: str,
    fields: list[XSDField],
    main_sheet_name: str = "main",
    sheet_prefix: Optional[str] = None,
):
    wb = Workbook()

    # Remove initial sheets
    for sheetname in wb.sheetnames:
        wb.remove(wb[sheetname])

    # Create main sheet
    ws = wb.create_sheet(main_sheet_name)
    ws.cell(1, 1, YEAR_LABEL)
    for index, field in enumerate(
        field.name for field in fields if field.type == FieldType.COMMON
    ):
        ws.cell(index + 2, 1, field)

    # Create other sheets for complex data
    # for field in fields:
    #     if field.type == FieldType.COMMON:
    #         # Already handled
    #         continue

    #     ws = wb.create_sheet(f"{sheet_prefix or ''}{field.name}")
    #     if field.type == FieldType.SEQUENCE:
    #         for index, child in enumerate(field.children):
    #             ws.cell(1, index + 1, child)

    wb.save(filename=xlsx_filepath)


def xsd_to_xlsx(
    xsd_filepath: str,
    xlsx_filepath: str,
    main_sheet_name: str = "main",
    sheet_prefix: Optional[str] = None,
):
    fields = extract_xsd_fields(xsd_filepath)

    create_xlsx_template(
        xlsx_filepath=xlsx_filepath,
        fields=fields,
        main_sheet_name=main_sheet_name,
        sheet_prefix=sheet_prefix,
    )
