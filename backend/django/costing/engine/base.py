import uuid
from pathlib import Path
from typing import Any, Optional

from openpyxl import load_workbook
from openpyxl.utils.cell import range_to_tuple
from openpyxl.worksheet.worksheet import Worksheet

from costing.engine.constants import YEAR_LABEL
from costing.engine.ssh import connect_to_engine

ENGINE_SHARED_DIR = "/usr/src/costing/engine_shared"


def store_inputs(
    *,
    worksheet: "Worksheet",
    inputs: dict[str, Any],
    col: int,
):
    row = 1
    while key := worksheet.cell(row, 1).value:
        if key in inputs:
            try:
                worksheet.cell(row, col, inputs[key])
            except ValueError as ex:
                raise ValueError(
                    f"Cannot store value '{inputs[key]}' of input {key}"
                ) from ex
        row += 1


# def store_sequence_inputs(
#     *,
#     worksheet: "Worksheet",
#     inputs: list[dict[str, Any]],
# ):
#     col = 1
#     header = {}
#     while header_value := worksheet.cell(1, col).value:
#         header[header_value] = col
#         col += 1

#     for index, row_data in enumerate(inputs):
#         row = index + 2
#         for sub_input_key, value in row_data.items():
#             worksheet.cell(row, header[sub_input_key], value)


def create_input_file(
    *,
    filepath: str,
    template_file: str,
    common_inputs_sheetname: str,
    # complex_inputs_prefix: str,
    inputs: dict[str, Any],
    logger: Optional[Any] = None,
) -> None:
    if logger:
        logger.info("Loading template file from %s", template_file)
    workbook = load_workbook(template_file, data_only=False)

    if logger:
        logger.info("Storing inputs %s", inputs)

    years = sorted(inputs.keys())
    for index, year in enumerate(years):
        store_inputs(
            worksheet=workbook[common_inputs_sheetname],
            inputs={**inputs[year], YEAR_LABEL: year},
            col=index + 2,
        )
    # for complex_sheetname in [
    #     sheetname
    #     for sheetname in workbook.sheetnames
    #     if sheetname.startswith(complex_inputs_prefix)
    # ]:
    #     store_sequence_inputs(
    #         worksheet=workbook[complex_sheetname],
    #         inputs=inputs.get(complex_sheetname[len(complex_inputs_prefix) :], []),
    #     )

    if logger:
        logger.info("Saving input file to %s", filepath)
    workbook.save(filepath)
    workbook.close()


def create_output_file(
    *,
    input_filepath: str,
    output_filepath: str,
    logger: Optional[Any] = None,
) -> None:
    if logger:
        logger.info("Connecting to engine via ssh")
    ssh = connect_to_engine()

    # Note: LibreOffice has an issue when overwriting files
    # so be sure to use a different directory for the output file
    output_dir = Path(output_filepath).parent
    command = (
        "libreoffice --headless --calc --convert-to xlsx "
        f"--outdir {output_dir} {input_filepath}"
    )
    if logger:
        logger.info("Running command %s", command)
    stdin, stdout, stderr = ssh.exec_command(command)

    out1 = stdout.read().decode()
    out2 = stdout.read().decode()
    if logger:
        logger.info(out1)
        logger.info(out2)

    ssh.close()


def read_outputs(
    *,
    filepath: str,
    outputs: dict[Any, Any],
    logger: Optional[Any] = None,
) -> dict[Any, Any]:
    if logger:
        logger.info("Loading output file from %s", filepath)
    workbook = load_workbook(filepath, data_only=True)

    def recursively_parse_value(value):
        if isinstance(value, str):
            sheetname, (col, row, *_) = range_to_tuple(value)
            cell = workbook[sheetname][row][col - 1]
            return cell.value
        if isinstance(value, dict):
            return {k: recursively_parse_value(v) for k, v in value.items()}
        if isinstance(value, list):
            return [recursively_parse_value(v) for v in value]
        if isinstance(value, tuple):
            return tuple(recursively_parse_value(v) for v in value)
        return value

    output_values = recursively_parse_value(outputs)

    if logger:
        logger.info("Readed output values: %s", output_values)

    workbook.close()

    return output_values


def process(
    *,
    template_file: str,
    common_inputs_sheetname: str,
    complex_inputs_prefix: str,
    inputs: dict[str, Any],
    outputs: dict[str, str],
    keep_files: bool = False,
    process_name: Optional[str] = None,
    logger: Optional[Any] = None,
) -> dict[Any, Any]:
    _process_name = process_name or str(uuid.uuid4())

    input_filepath = f"{ENGINE_SHARED_DIR}/inputs/{_process_name}.xlsx"
    output_filepath = f"{ENGINE_SHARED_DIR}/outputs/{_process_name}.xlsx"

    create_input_file(
        filepath=input_filepath,
        template_file=template_file,
        common_inputs_sheetname=common_inputs_sheetname,
        complex_inputs_prefix=complex_inputs_prefix,
        inputs=inputs,
        logger=logger,
    )

    create_output_file(
        input_filepath=input_filepath, output_filepath=output_filepath, logger=logger
    )

    output_values = read_outputs(
        filepath=output_filepath, outputs=outputs, logger=logger
    )

    if not keep_files:
        if logger:
            logger.info("Deleting input and output files")
        Path(input_filepath).unlink()
        Path(output_filepath).unlink()

    return output_values
