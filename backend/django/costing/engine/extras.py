from costing.engine.base import ENGINE_SHARED_DIR, create_input_file
from costing.engine.xsd_to_xlsx import xsd_to_xlsx
from costing.models.financial_statement import FinancialStatement


def crea_modello_base_per_michele():
    xsd_to_xlsx(
        xsd_filepath="common/xml_utils/xsd/PCI-2018-11-04/itcc-ci-2018-11-04.xsd",
        xlsx_filepath="costing/engine/templates/ModelloCreatoDaXSDPerMichele.xlsx",
        main_sheet_name="main",
    )


def riempi_modello_per_michele() -> None:
    items_by_years = {
        fs.year: fs.xbrl_data_dto.items for fs in FinancialStatement.objects.all()
    }

    create_input_file(
        filepath=f"{ENGINE_SHARED_DIR}/inputs/ModelloRiempitoPerMichele.xlsx",
        template_file="costing/engine/templates/ModelloCreatoDaXSDPerMichele.xlsx",
        inputs=items_by_years,
        common_inputs_sheetname="main",
    )
