import paramiko


def connect_to_engine() -> paramiko.SSHClient:
    hostname = "costing_engine"
    port = 22
    username = "engine"
    key_path = "../../docker/dev/engine_rsa"

    key = paramiko.RSAKey.from_private_key_file(key_path)
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())  # noqa: S507
    ssh.connect(hostname, port=port, username=username, pkey=key)

    return ssh
