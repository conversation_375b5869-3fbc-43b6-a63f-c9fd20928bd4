FROM --platform=linux/amd64 python:3.12-alpine3.20

# Initial setup
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONBREAKPOINT ipdb.set_trace
ENV COSTING_ENV dev

# Install dependencies
RUN apk update
RUN apk add --no-cache git openssh-client

# Configure SSH for git
RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN git config --global --add safe.directory /usr/src/costing

# Copy SSH keys for engine access
COPY docker/dev/engine_rsa /root/.ssh/id_rsa
RUN chmod 600 /root/.ssh/id_rsa

WORKDIR /usr/src/costing/backend/django

RUN python -m ensurepip --upgrade

ENTRYPOINT ["/usr/src/costing/docker/dev/costing_backend-entrypoint.sh"]