FROM python:3.12-slim

# Install dependencies
RUN apt-get update
RUN apt-get install -y libreoffice openssh-server
RUN mkdir /var/run/sshd

# Configure SSH server
RUN useradd -m -s /bin/bash engine
RUN mkdir -p /home/<USER>/.ssh
COPY docker/dev/engine_rsa.pub /home/<USER>/.ssh/authorized_keys
RUN chown -R engine:engine /home/<USER>/.ssh
RUN chmod 700 /home/<USER>/.ssh
RUN chmod 600 /home/<USER>/.ssh/authorized_keys
EXPOSE 22

WORKDIR /usr/src/costing/backend/django
