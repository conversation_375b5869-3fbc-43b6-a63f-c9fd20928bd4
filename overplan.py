import datetime
from collections import defaultdict

from jet.services.shifts.dataclasses.shift_span import ShiftSpan
from jet.services.shifts.dataclasses.week import Week
from jet.services.shifts.enums import WorkKind
from jet.services.shifts.filters import (
    filter_spans_in_period,
)
from project.utils import date as date_utils

DURATION_ZERO = datetime.timedelta()
TO_ALLOCATE_PER_ITERATION = datetime.timedelta(minutes=30)


def overwrite_spans_work_kind(
    spans: list[ShiftSpan], duration: datetime.timedelta, work_kind: WorkKind
) -> list[ShiftSpan]:
    """
    Overwrites the work_kind of all the passed spans
    until the specified duration is reached.
    """
    if not spans or not duration:
        return spans

    remained_duration = duration
    _spans = ShiftSpan.sorted(spans)
    overwritten_spans = []

    while remained_duration > datetime.timedelta() and _spans:
        old_span = _spans.pop()
        if remained_duration >= old_span.duration:
            overwritten_spans.append(
                old_span.copy(work_kind=work_kind, is_generated=True)
            )
            remained_duration -= old_span.duration
        else:
            splitted_spans = old_span.split(
                [old_span.duration - remained_duration, remained_duration]
            )
            splitted_spans[1].work_kind = work_kind
            splitted_spans[1].is_generated = True
            overwritten_spans.extend(splitted_spans)
            remained_duration = datetime.timedelta()

    return overwritten_spans + _spans


class ShiftWeek:
    def __init__(
        self,
        week: Week,
        spans: list[ShiftSpan],
        weekly_duration: datetime.timedelta,
        ccnl_weekly_duration: datetime.timedelta,
    ):
        self.week = week
        self.spans = filter_spans_in_period(
            spans, self.week.start_date, self.week.end_date
        )
        self.ccnl_weekly_duration = ccnl_weekly_duration
        self.weekly_duration = weekly_duration

    @property
    def worked_spans(self):
        return [span for span in self.spans if span.is_working]

    @property
    def planned_duration(self):
        return sum(
            (span.duration for span in self.spans),
            DURATION_ZERO,
        )

    @property
    def additional_duration(self):
        return sum(
            (
                span.duration
                for span in self.spans
                if span.work_kind == WorkKind.ADDITIONAL
            ),
            DURATION_ZERO,
        )

    @property
    def overtime_duration(self):
        return sum(
            (span.duration for span in self.spans if span.is_overtime),
            DURATION_ZERO,
        )

    @property
    def expected_overtime_duration(self):
        return max(DURATION_ZERO, self.planned_duration - self.ccnl_weekly_duration)

    @property
    def expected_additional_duration(self):
        return max(
            DURATION_ZERO,
            self.planned_duration
            - self.weekly_duration
            - self.expected_overtime_duration,
        )

    @property
    def unplanned_overtime_duration(self):
        return max(
            DURATION_ZERO, self.expected_overtime_duration - self.overtime_duration
        )

    @property
    def unplanned_additional_duration(self):
        return max(
            DURATION_ZERO, self.expected_additional_duration - self.additional_duration
        )

    def get_overplan_allocation(
        self,
    ) -> tuple[
        dict[datetime.date, datetime.timedelta], dict[datetime.date, datetime.timedelta]
    ]:
        duration_by_dates = {
            date: sum(
                (
                    span.duration
                    for span in self.spans
                    if span.date == date and span.work_kind == WorkKind.STANDARD
                ),
                DURATION_ZERO,
            )
            for date in date_utils.daterange(self.week.start_date, self.week.end_date)
        }

        def compute_allocation(
            duration_to_allocate: datetime.timedelta,
        ) -> dict[datetime.date, datetime.timedelta]:
            target_allocation = defaultdict(datetime.timedelta)
            while (
                duration_to_allocate > DURATION_ZERO
                and sum(duration_by_dates.values(), DURATION_ZERO) > DURATION_ZERO
            ):
                iteration_allocation = min(
                    duration_to_allocate, TO_ALLOCATE_PER_ITERATION
                )
                date_to_allocate = min(
                    [
                        date
                        for date, duration in duration_by_dates.items()
                        if duration == max(duration_by_dates.values())
                    ]
                )
                target_allocation[date_to_allocate] += iteration_allocation
                duration_by_dates[date_to_allocate] -= iteration_allocation
                duration_to_allocate -= iteration_allocation
            return dict(target_allocation)

        overtime_allocation = compute_allocation(self.unplanned_overtime_duration)
        additional_allocation = compute_allocation(self.unplanned_additional_duration)

        return overtime_allocation, additional_allocation

    def replace_span(self, span: ShiftSpan, replace_spans: list[ShiftSpan]) -> None:
        index = self.spans.index(span)
        self.spans = self.spans[:index] + replace_spans + self.spans[index + 1 :]

    def replace_overtime(
        self, duration: datetime.timedelta, work_kind: WorkKind
    ) -> None:
        overtime_spans = ShiftSpan.sorted(
            [span for span in self.spans if span.work_kind == WorkKind.OVERTIME]
        )
        overwritten_spans = overwrite_spans_work_kind(
            overtime_spans, duration, work_kind
        )
        self.spans = ShiftSpan.sorted(
            [span for span in self.spans if span not in overtime_spans]
            + overwritten_spans
        )

    def replace_worked_time(
        self, date: datetime.date, duration: datetime.timedelta, work_kind: WorkKind
    ) -> None:
        date_spans = ShiftSpan.sorted(
            [
                span
                for span in filter_spans_in_period(self.spans, date, date)
                if span.work_kind == WorkKind.STANDARD
            ]
        )
        overwritten_spans = overwrite_spans_work_kind(date_spans, duration, work_kind)
        self.spans = ShiftSpan.sorted(
            [span for span in self.spans if span not in date_spans] + overwritten_spans
        )


def parse_overplan_spans(
    *,
    week: Week,
    spans: list[ShiftSpan],
    ccnl_weekly_duration: datetime.timedelta,
    weekly_duration: datetime.timedelta,
    overtime_work_kind: WorkKind,
    additional_work_kind: WorkKind,
) -> list[ShiftSpan]:
    """
    Handles overplanned working hours by assigning appropriate work kinds.

    When shifts exceed an employee's standard working hours,
    a different kind is assigned to the extra hours.

    `additional_work_kind` is assigned to hours that exceed
    the employee's personal schedule (e.g., part-time).

    `overtime_work_kind` is assigned to hours that exceed
    the weekly working time defined by the CCNL.
    """
    shift_week = ShiftWeek(
        week=week,
        spans=spans,
        weekly_duration=weekly_duration,
        ccnl_weekly_duration=ccnl_weekly_duration,
    )

    # Convert exceeded overtime to additional
    if shift_week.overtime_duration > shift_week.expected_overtime_duration:
        shift_week.replace_overtime(
            duration=shift_week.overtime_duration
            - shift_week.expected_overtime_duration,
            work_kind=additional_work_kind,
        )

    overtime_allocation, additional_allocation = shift_week.get_overplan_allocation()
    for date, duration in overtime_allocation.items():
        shift_week.replace_worked_time(
            date=date, duration=duration, work_kind=overtime_work_kind
        )
    for date, duration in additional_allocation.items():
        shift_week.replace_worked_time(
            date=date, duration=duration, work_kind=additional_work_kind
        )

    return [span.copy() for span in shift_week.spans]
