import datetime
from collections import namedtuple
from enum import StrEnum

from jet.services.shifts.dataclasses.shift_slot import ShiftSlot
from jet.services.shifts.dataclasses.shift_span import ShiftSpan
from jet.services.shifts.dataclasses.week import Week
from jet.services.shifts.filters import (
    filter_spans_in_period,
)
from project.utils import date as date_utils

DURATION_ZERO = datetime.timedelta()
COMMON_WORKING_DAYS_COUNT = 5
MINIMUM_DURATION_TO_ALLOCATE = datetime.timedelta(minutes=30)
MAXIMUM_DURATION_TO_ALLOCATE = datetime.timedelta(hours=8)


class DayWithSlots:
    def __init__(self, date: datetime.date, initial_slots: list[ShiftSlot]):
        self.date = date
        self.initial_slots = initial_slots
        self.allocated_slots = []

    @property
    def starts_at(self) -> datetime.datetime:
        return datetime.datetime.combine(self.date, datetime.time.min)

    @property
    def ends_at(self) -> datetime.datetime:
        return datetime.datetime.combine(
            self.date + datetime.timedelta(days=1), datetime.time.min
        )

    @property
    def all_slots(self) -> list[ShiftSlot]:
        return ShiftSlot.merge_consecutives(
            [*self.initial_slots, *self.allocated_slots]
        )

    @property
    def free_slots(self) -> list[ShiftSlot]:
        _free_slots = []
        all_slot = self.all_slots
        datetimes = sorted(
            {
                self.starts_at,
                *[
                    datetime.datetime.combine(self.date, slot.start_time)
                    for slot in all_slot
                ],
                *[
                    datetime.datetime.combine(self.date, slot.end_time)
                    for slot in all_slot
                ],
                self.ends_at,
            }
        )
        for i in range(1, len(datetimes)):
            starts_at = datetimes[i - 1]
            ends_at = datetimes[i]
            current_slot = ShiftSlot(
                start_time=starts_at.time(),
                end_time=ends_at.time(),
                ends_in_next_day=ends_at.date() != self.date,
            )
            if not any(True for slot in all_slot if slot.intersect(current_slot)):
                _free_slots.append(current_slot)

        return ShiftSlot.merge_consecutives(_free_slots)

    @property
    def first_free_slot(self) -> ShiftSlot | None:
        return next(iter(self.free_slots), None)

    @property
    def all_allocated_duration(self) -> datetime.timedelta:
        return sum(
            (slot.duration for slot in self.all_slots),
            DURATION_ZERO,
        )

    @property
    def free_duration(self) -> datetime.timedelta:
        return sum(
            (slot.duration for slot in self.free_slots),
            DURATION_ZERO,
        )

    def allocate_slot(self, slot: ShiftSlot) -> None:
        self.allocated_slots = ShiftSlot.merge_consecutives(
            [*self.allocated_slots, slot.copy()]
        )


class GroupKind(StrEnum):
    HOLIDAY = "HOLIDAY"
    WORKING = "WORKING"
    NON_WORKING = "NON_WORKING"


DateWithKind = namedtuple("DateWithKind", ["date", "kind"])


def __free_slots_to_allocate_exists(
    current_days_with_slots: list[DayWithSlots],
) -> bool:
    return any(
        sd.free_duration > DURATION_ZERO
        and sd.all_allocated_duration < MAXIMUM_DURATION_TO_ALLOCATE
        for sd in current_days_with_slots
    )


def get_ordered_date_groups_for_underplan(
    week: Week, spans: list[ShiftSpan], holidays: list[datetime.date]
) -> list[list[datetime.date]]:
    dates_with_kinds = [
        DateWithKind(
            date=dt,
            kind=(
                GroupKind.HOLIDAY
                if date_utils.is_sunday(dt) or dt in holidays
                else (
                    GroupKind.WORKING
                    if any(
                        True
                        for span in spans
                        if span.ref_date == dt and not span.is_holiday
                    )
                    else GroupKind.NON_WORKING
                )
            ),
        )
        for dt in week.daterange()
    ]
    days_with_spans_count = len(
        [
            dt
            for dt in week.daterange()
            if any(True for span in spans if span.ref_date == dt)
        ]
    )

    date_groups = []

    if days_with_spans_count < COMMON_WORKING_DAYS_COUNT:
        # When there are less working days than the common working days count:

        # First, fill the non-working days, one by one, not as single group
        for date, kind in dates_with_kinds:
            if kind == GroupKind.NON_WORKING:
                date_groups.append([date])

        # Then, fill the working days as a single group
        date_groups.append(
            [date for date, kind in dates_with_kinds if kind == GroupKind.WORKING]
        )
    else:
        # When there are more or equal working days than the common working days count:

        # First, fill the working days as a single group
        date_groups.append(
            [date for date, kind in dates_with_kinds if kind == GroupKind.WORKING]
        )

        # Then, fill the non-working days, one by one, not as single group
        for date, kind in dates_with_kinds:
            if kind == GroupKind.NON_WORKING:
                date_groups.append([date])

    # Sunday and holidays are the last date-group to be filled
    date_groups.append(
        [date for date, kind in dates_with_kinds if kind == GroupKind.HOLIDAY]
    )

    return date_groups


def compute_underplanned_slots(
    *,
    week: Week,
    spans: list[ShiftSpan],
    under_planned_duration: datetime.timedelta,
    holidays: list[datetime.date],
) -> list[tuple[datetime.datetime, datetime.datetime]]:
    duration_to_be_allocated = under_planned_duration
    spans_in_week = filter_spans_in_period(spans, week.start_date, week.end_date)

    # Creates a list of DayWithSlots for each day in the week
    days_with_slots = [
        DayWithSlots(
            date=date,
            initial_slots=[
                ShiftSlot(
                    start_time=span.starts_at.time(),
                    end_time=span.ends_at.time(),
                    ends_in_next_day=span.ends_at.date() > date,
                )
                for span in filter_spans_in_period(spans_in_week, date, date)
            ],
        )
        for date in date_utils.daterange(week.start_date, week.end_date)
    ]

    # Get the date groups ordered by priority
    # The first group should be filled first
    date_groups_to_be_fillled = get_ordered_date_groups_for_underplan(
        week=week, spans=spans_in_week, holidays=holidays
    )

    for dates in date_groups_to_be_fillled:
        current_days_with_slots = [
            slot for slot in days_with_slots if slot.date in dates
        ]

        max_iterations = 30  # Stop execution when reach 0
        while (
            max_iterations > 0
            and duration_to_be_allocated > DURATION_ZERO
            and __free_slots_to_allocate_exists(current_days_with_slots)
        ):
            max_iterations -= 1

            # Sort the days by free duration
            ordered_days_with_slots = sorted(
                current_days_with_slots, key=lambda s: s.free_duration, reverse=True
            )

            first_free_day = ordered_days_with_slots[0]
            second_free_day_duration = (
                ordered_days_with_slots[1].free_duration
                if len(ordered_days_with_slots) > 1
                else DURATION_ZERO
            )
            duration_to_allocate = min(
                # Time to allocate against the second free day
                max(
                    first_free_day.free_duration - second_free_day_duration,
                    MINIMUM_DURATION_TO_ALLOCATE,
                ),
                # Maximum time to allocate in day
                MAXIMUM_DURATION_TO_ALLOCATE - first_free_day.all_allocated_duration,
                # Free time in day
                first_free_day.free_duration,
                # Remained unplanned time to be allocated
                duration_to_be_allocated,
            )

            free_slot = first_free_day.first_free_slot.copy()
            free_slot.end_time = (
                datetime.datetime.combine(first_free_day.date, free_slot.start_time)
                + duration_to_allocate
            ).time()
            free_slot.ends_in_next_day = free_slot.end_time < free_slot.start_time
            first_free_day.allocate_slot(free_slot)
            duration_to_be_allocated -= duration_to_allocate

    allocated_slots = []
    for shift_day in days_with_slots:
        for slot in shift_day.allocated_slots:
            allocated_slots.append(
                (
                    datetime.datetime.combine(shift_day.date, slot.start_time),
                    datetime.datetime.combine(shift_day.date, slot.end_time),
                )
            )
    return allocated_slots
